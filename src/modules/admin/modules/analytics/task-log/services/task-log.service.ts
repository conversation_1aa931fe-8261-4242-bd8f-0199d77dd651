import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { LogTask } from '../../../../../../common/modules/database/entities/log-task.entity';
import { LogTaskField } from '../../../../../../common/modules/database/entities/log-task-field.entity';
import { GetTaskLogDto } from '../dto/get-task-log.dto';
import { User } from '../../../../../../common/modules/database/entities/user.entity';
import { Branch } from '../../../../../../common/modules/database/entities/branch.entity';
import { OrderDirection } from '../../../../../../common/dto/base-query.dto';
import { ReportFormatType } from '../../../../../../common/interfaces/report-format-type.enum';
import { StorageService } from '../../../../../../common/modules/storage/services/storage.service';
import { Role } from '../../../../../../common/modules/database/entities/role.entity';
import { Device } from '../../../../../../common/modules/database/entities/device.entity';
import { Task } from '../../../../../../common/modules/database/entities/task.entity';
import { Label } from '../../../../../../common/modules/database/entities/label.entity';
import { ReportFileSave } from '../../../../../../common/interfaces/report-file-save.enum';
import {
  TaskLogFilters,
  TaskLogGenerateDocumentService,
} from '../../../../../../common/modules/analytics/task/services/task-log-generate-document.service';
import { Timezone } from '../../../../../../common/modules/database/entities/timezone.entity';
import { TaskAnalyticService } from '../../../../../../common/modules/analytics/task/services/task-analytic.service';

/**
 * Service responsible for retrieving and filtering task logs
 * Implements complex filtering logic with support for:
 * - Date/time ranges
 * - Branch-based access control
 * - User and device filtering
 * - Label-based filtering
 * - Pagination and sorting
 */
@Injectable()
export class TaskLogService {
  constructor(
    @InjectRepository(LogTask)
    private readonly logTaskRepository: Repository<LogTask>,
    @InjectRepository(LogTaskField)
    private readonly logTaskFieldRepository: Repository<LogTaskField>,
    private storageService: StorageService,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(Label)
    private labelRepository: Repository<Label>,
    @InjectRepository(Timezone)
    private timezoneRepository: Repository<Timezone>,
    private taskLogGenerateDocumentService: TaskLogGenerateDocumentService,
    private taskAnalyticService: TaskAnalyticService,
  ) {}

  /**
   * Retrieves task logs with comprehensive filtering options
   *
   * @param query - DTO containing all filter parameters
   * @param user - Current authenticated user
   * @param branch - Current selected branch
   * @returns Paginated task logs with metadata
   */
  async findAll(query: GetTaskLogDto, user: User, branch: Branch) {
    // Extract and set default values for query parameters
    let {
      page = 1,
      limit = 10,
      order_by = 'original_submitted_time',
      order_direction = OrderDirection.DESC,
      start_date,
      end_date,
      start_time = '00:00:00',
      end_time = '23:59:59',
      branch_id,
      role_id,
      user_id,
      user_labels,
      device_id,
      device_labels,
      task_id,
    } = query;

    const timezone = await this.timezoneRepository.findOne({
      where: { id: branch.timezone_id },
    });

    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    const { data, total } = await this.taskAnalyticService.getTasks(
      branch.parent_id,
      {
        startDate: start_date,
        endDate: end_date,
        startTime: start_time,
        endTime: end_time,
        branchId: branch_id,
        roleId: role_id,
        userId: user_id,
        userLabels: user_labels,
        deviceLabels: device_labels,
        deviceId: device_id,
        taskId: task_id,
        limit,
        page,
        orderBy: order_by,
        orderDirection: order_direction,
      },
      {
        timezone: timezone,
        user: user,
      },
    );

    return {
      data: data,
      meta: {
        page: page,
        limit: limit,
        total: total,
        total_pages: Math.ceil(total / limit),
      },
    };
  }

  async exportTaskLogs(
    type: ReportFormatType,
    query: GetTaskLogDto,
    user: User,
    branch: Branch,
    saveFileType: ReportFileSave,
  ) {
    const modifiedQuery = {
      ...query,
      page: 1,
      limit: 1000,
    };

    const { data } = await this.findAll(modifiedQuery, user, branch);

    const filters: TaskLogFilters = {
      startDate: query.start_date || null,
      endDate: query.end_date || null,
      startTime: query.start_time || null,
      endTime: query.end_time || null,
      branch: null,
      role: null,
      user: null,
      user_labels: [],
      device: null,
      device_labels: [],
      task: null,
    };

    if (query.branch_id) {
      const branch = await this.branchRepository.findOne({
        where: { id: query.branch_id },
      });
      if (branch) {
        filters.branch = branch;
      }
    }

    if (query.role_id) {
      const role = await this.roleRepository.findOne({
        where: { id: query.role_id },
      });
      if (role) {
        filters.role = role;
      }
    }

    if (query.user_id) {
      const user = await this.userRepository.findOne({
        where: { id: query.user_id },
      });
      if (user) {
        filters.user = user;
      }
    } else if (query.user_labels && query.user_labels.length > 0) {
      const userLabels = await this.labelRepository.find({
        where: { id: In(query.user_labels) },
      });
      if (userLabels) {
        filters.user_labels = userLabels;
      }
    }

    if (query.device_id) {
      const device = await this.deviceRepository.findOne({
        where: { id: query.device_id },
      });
      if (device) {
        filters.device = device;
      }
    } else if (query.device_labels && query.device_labels.length > 0) {
      const deviceLabels = await this.labelRepository.find({
        where: { id: In(query.device_labels) },
      });
      if (deviceLabels) {
        filters.device_labels = deviceLabels;
      }
    }

    if (query.task_id) {
      const task = await this.taskRepository.findOne({
        where: { id: query.task_id },
      });
      if (task) {
        filters.task = task;
      }
    }

    if (type === ReportFormatType.PDF) {
      const { buffer, filename } =
        await this.taskLogGenerateDocumentService.generatePDF(
          data,
          filters,
          branch.timezone,
        );

      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/pdf',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    } else if (type === ReportFormatType.SPREADSHEET) {
      const { buffer, filename } =
        await this.taskLogGenerateDocumentService.generateSpreadsheet(
          data,
          filters,
          branch.timezone,
        );

      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    }

    throw new Error('Spreadsheet export not yet implemented');
  }

  async exportTaskLogById(
    id: number,
    type: ReportFormatType,
    user: User,
    branch: Branch,
    saveFileType: ReportFileSave,
  ) {
    const taskLog = await this.logTaskRepository.findOne({
      where: { id, parent_branch_id: branch.parent_id },
      relations: ['branch', 'task', 'role', 'user', 'device', 'timezone'],
    });

    if (!taskLog) {
      throw new NotFoundException(`Task Log with ID ${id} not found`);
    }

    if (type === ReportFormatType.PDF) {
      const { buffer, filename } =
        await this.taskLogGenerateDocumentService.generatePDFById(
          taskLog,
          taskLog.timezone,
        );
      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/pdf',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    } else if (type === ReportFormatType.SPREADSHEET) {
      const { buffer, filename } =
        await this.taskLogGenerateDocumentService.generateSpreadsheetById(
          taskLog,
          taskLog.timezone,
        );
      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    }

    throw new Error('Spreadsheet export not yet implemented');
  }
}
