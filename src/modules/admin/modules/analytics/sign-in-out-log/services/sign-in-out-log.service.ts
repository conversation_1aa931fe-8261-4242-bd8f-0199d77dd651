import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, In, Repository } from 'typeorm';
import { LogUserDevice } from '../../../../../../common/modules/database/entities/log-user-device.entity';
import { GetSignInOutLogDto } from '../dto/get-sign-in-out-log.dto';
import { User } from '../../../../../../common/modules/database/entities/user.entity';
import { Branch } from '../../../../../../common/modules/database/entities/branch.entity';
import { OrderDirection } from '../../../../../../common/dto/base-query.dto';
import { ReportFileSave } from '../../../../../../common/interfaces/report-file-save.enum';
import { SignInOutLogGenerateDocumentService } from '../../../../../../common/modules/analytics/sign-in-out/services/sign-in-out-log-generate-document.service';
import { StorageService } from '../../../../../../common/modules/storage/services/storage.service';
import { Label } from '../../../../../../common/modules/database/entities/label.entity';
import { Device } from '../../../../../../common/modules/database/entities/device.entity';
import { ReportFormatType } from '../../../../../../common/interfaces/report-format-type.enum';
import { parseTimeDaily } from '../../../../../../common/utils/time-utils';
import { SignInOutLogFiltersGenerateDocument } from '../../../../../../common/modules/analytics/sign-in-out/services/sign-in-out-log-generate-document.service';
import { Timezone } from '../../../../../../common/modules/database/entities/timezone.entity';
import { SignInOutAnalyticService } from '../../../../../../common/modules/analytics/sign-in-out/services/sign-in-out-analytic.service';

@Injectable()
export class SignInOutLogService {
  constructor(
    @InjectRepository(LogUserDevice)
    private readonly logUserDeviceRepository: Repository<LogUserDevice>,
    private signInOutLogGenerateDocumentService: SignInOutLogGenerateDocumentService,
    private storageService: StorageService,
    @InjectRepository(Branch)
    private readonly branchRepository: Repository<Branch>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Label)
    private readonly labelRepository: Repository<Label>,
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
    @InjectRepository(Timezone)
    private readonly timezoneRepository: Repository<Timezone>,
    private readonly signInOutAnalyticService: SignInOutAnalyticService,
  ) {}

  /**
   * Retrieves sign-in/out logs with comprehensive filtering options
   *
   * @param query - DTO containing all filter parameters
   * @param user - Current authenticated user
   * @param branch - Current selected branch
   * @returns Paginated sign-in/out logs with metadata
   */
  async findAll(query: GetSignInOutLogDto, user: User, branch: Branch) {
    // Extract and set default values for query parameters
    let {
      page = 1,
      limit = 10,
      order_by = 'event_time',
      order_direction = OrderDirection.DESC,
      start_date,
      end_date,
      start_time = '00:00:00',
      end_time = '23:59:59',
      branch_id,
      user_id,
      user_labels,
      device_id,
      device_labels,
    } = query;

    const timezone = await this.timezoneRepository.findOne({
      where: {
        id: branch.timezone_id,
      },
    });

    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    const getSignInOut = await this.signInOutAnalyticService.getSignInOut(
      branch.parent_id,
      {
        startDate: start_date,
        endDate: end_date,
        startTime: start_time,
        endTime: end_time,
        branchId: branch_id,
        userId: user_id,
        userLabels: user_labels,
        deviceId: device_id,
        deviceLabels: device_labels,
        limit,
        page,
        orderBy: order_by,
        orderDirection: order_direction,
      },
      {
        timezone,
        user,
      },
    );

    return {
      data: getSignInOut.data,
      meta: {
        total: getSignInOut.total,
        page: page,
        limit: limit,
        total_pages: Math.ceil(getSignInOut.total / limit),
      },
    };
  }

  async exportSignInOutLogs(
    type: ReportFormatType,
    query: GetSignInOutLogDto,
    user: User,
    branch: Branch,
    saveFileType: ReportFileSave,
  ) {
    const { data } = await this.findAll(query, user, branch);

    const filters: SignInOutLogFiltersGenerateDocument = {
      startDate: query.start_date || null,
      endDate: query.end_date || null,
      startTime: query.start_time || null,
      endTime: query.end_time || null,
      branch: null,
      user: null,
      user_labels: [],
      device: null,
      device_labels: [],
    };

    // Filters
    if (query.branch_id) {
      filters.branch = await this.branchRepository.findOne({
        where: { id: query.branch_id },
      });
    }
    if (query.user_id) {
      filters.user = await this.userRepository.findOne({
        where: { id: query.user_id },
      });
    }
    if (query.user_labels) {
      filters.user_labels = await this.labelRepository.find({
        where: { id: In(query.user_labels) },
      });
    }
    if (query.device_id) {
      filters.device = await this.deviceRepository.findOne({
        where: { id: query.device_id },
      });
    }
    if (query.device_labels) {
      filters.device_labels = await this.labelRepository.find({
        where: { id: In(query.device_labels) },
      });
    }

    if (type === ReportFormatType.PDF) {
      const { buffer, filename } =
        await this.signInOutLogGenerateDocumentService.generatePDF(
          data,
          filters,
          branch.timezone,
        );

      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/pdf',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    } else if (type === ReportFormatType.SPREADSHEET) {
      const { buffer, filename } =
        await this.signInOutLogGenerateDocumentService.generateSpreadsheet(
          data,
          filters,
          branch.timezone,
        );

      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    }

    throw new Error('Spreadsheet export not yet implemented');
  }

  async exportSignInOutLogById(
    id: number,
    type: ReportFormatType,
    user: User,
    branch: Branch,
    saveFileType: ReportFileSave,
  ) {
    // Find Sign In/Out Log by ID and parent branch
    const signInOutLog = await this.logUserDeviceRepository.findOne({
      where: { id, parent_branch_id: branch.parent_id },
      relations: ['parent_branch', 'user', 'device', 'timezone'],
    });

    // If Sign In/Out Log is not found, throw exception
    if (!signInOutLog) {
      throw new NotFoundException(`Sign In/Out Log with ID ${id} not found`);
    }

    // Handle PDF export
    if (type === ReportFormatType.PDF) {
      const { buffer, filename } =
        await this.signInOutLogGenerateDocumentService.generatePDFById(
          signInOutLog,
          branch.timezone,
        );
      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/pdf',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    }
    // Handle spreadsheet export
    else if (type === ReportFormatType.SPREADSHEET) {
      const { buffer, filename } =
        await this.signInOutLogGenerateDocumentService.generateSpreadsheetById(
          signInOutLog,
          branch.timezone,
        );
      if (saveFileType === ReportFileSave.LINK) {
        const destination = `reports/${filename}`;
        const publicUrl = await this.storageService.uploadBuffer(
          buffer,
          destination,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );

        return {
          url: publicUrl,
          filename: filename,
        };
      } else {
        return {
          buffer: buffer,
          filename: filename,
        };
      }
    }
    throw new Error('Spreadsheet export not yet implemented');
  }
}
