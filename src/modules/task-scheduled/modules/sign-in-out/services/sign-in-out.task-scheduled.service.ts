import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
import { Scheduler } from '../../../../../common/modules/database/entities/scheduler.entity';
import { SignInOutAnalyticService } from '../../../../../common/modules/analytics/sign-in-out/services/sign-in-out-analytic.service';
import {
  SignInOutLogGenerateDocumentService,
  SignInOutLogFiltersGenerateDocument,
} from '../../../../../common/modules/analytics/sign-in-out/services/sign-in-out-log-generate-document.service';
import { EFrequency } from '../../../../../common/modules/database/entities/frequency.entity';
import { ReportFormatType } from '../../../../../common/interfaces/report-format-type.enum';
import { determineDateRangeByFrequency } from '../../../../../common/utils/scheduler.util';
import { OrderDirection } from '../../../../../common/dto/base-query.dto';
import { SignInOutAnalyticParams } from '../../../../../common/modules/analytics/sign-in-out/interfaces/sign-in-out-analytic.interface';
import { LogUserDevice } from '../../../../../common/modules/database/entities/log-user-device.entity';
import {
  ISchedulerService,
  ITaskScheduledPreparedData,
} from '../../../interfaces/task-scheduled.interfaces';

@Injectable()
export class SignInOutTaskScheduledService
  implements ISchedulerService<LogUserDevice>
{
  constructor(
    private signInOutAnalyticService: SignInOutAnalyticService,
    private signInOutLogGenerateDocumentService: SignInOutLogGenerateDocumentService,
  ) {}

  async fetchDataAndGenerateDocument(scheduler: Scheduler) {
    const currentDateTime = dayjs()
      .utc()
      .add(scheduler.branch.timezone.gmt_offset, 'hours')
      .set('second', 0)
      .set('millisecond', 0);

    const frequency: EFrequency = parseInt(
      scheduler.frequency_id as unknown as string,
    ) as EFrequency;

    const { startDate, endDate } = determineDateRangeByFrequency(
      frequency,
      currentDateTime,
    );

    const params: SignInOutAnalyticParams = {
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
      startTime: scheduler.start_time || '00:00:00',
      endTime: scheduler.end_time || '23:59:59',
      branchId: scheduler.selected_branch_id || null,
      userId: scheduler.user_id || null,
      userLabels:
        scheduler.user_labels?.length > 0
          ? scheduler.user_labels.map(label => label.id)
          : null,
      deviceId: scheduler.device_id || null,
      deviceLabels:
        scheduler.device_labels?.length > 0
          ? scheduler.device_labels.map(label => label.id)
          : null,
      orderBy: 'event_time',
      orderDirection: OrderDirection.DESC,
    };

    const signInOutData = await this.signInOutAnalyticService.getSignInOut(
      scheduler.branch_id,
      params,
      {
        timezone: scheduler.branch.timezone,
        user: scheduler.user,
      },
    );

    if (scheduler.stop_if_blank && signInOutData.total === 0) {
      return {
        data: [],
        total: 0,
        attachments: [],
      };
    }

    const filters: SignInOutLogFiltersGenerateDocument = {
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
      startTime: scheduler.start_time || '00:00:00',
      endTime: scheduler.end_time || '23:59:59',
      branch: scheduler.selected_branch,
      user: scheduler.user,
      user_labels: scheduler.user_labels || [],
      device: scheduler.device,
      device_labels: scheduler.device_labels || [],
    };

    const results: ITaskScheduledPreparedData<LogUserDevice> = {
      data: signInOutData.data,
      total: signInOutData.total,
      attachments: [],
    };

    if (scheduler.report_format_type === ReportFormatType.PDF) {
      const pdf = await this.signInOutLogGenerateDocumentService.generatePDF(
        signInOutData.data,
        filters,
        scheduler.branch.timezone,
      );

      results.attachments.push({
        content: pdf.buffer.toString('base64'),
        filename: `sign-in-out-report-${startDate.format('YYYY-MM-DD')}-${endDate.format('YYYY-MM-DD')}.pdf`,
        type: 'application/pdf',
        disposition: 'attachment',
      });
    } else if (scheduler.report_format_type === ReportFormatType.SPREADSHEET) {
      const excel =
        await this.signInOutLogGenerateDocumentService.generateSpreadsheet(
          signInOutData.data,
          filters,
          scheduler.branch.timezone,
        );

      results.attachments.push({
        content: excel.buffer.toString('base64'),
        filename: `sign-in-out-report-${startDate.format('YYYY-MM-DD')}-${endDate.format('YYYY-MM-DD')}.xlsx`,
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        disposition: 'attachment',
      });
    }

    return results;
  }
}
