import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import * as dayjs from 'dayjs';
import * as isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import * as isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import * as isBetween from 'dayjs/plugin/isBetween';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import {
  parseTimeDaily,
  parseTimeWithPrecision,
} from '../../../../utils/time-utils';
import { User } from '../../../database/entities/user.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { LogCheckpoint } from '../../../database/entities/log-checkpoint.entity';
import { Checkpoint } from '../../../database/entities/checkpoint.entity';
import {
  DateRotation,
  IntervalRotation,
  TimeAndRotation,
  TimeRotationAnalyticParams,
} from '../interfaces/time-rotation-analytic.interface';
import { OrderDirection } from '../../../../dto/base-query.dto';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

@Injectable()
export class TimeRotationAnalyticService {
  constructor(
    @InjectRepository(LogCheckpoint)
    private readonly logCheckpointRepository: Repository<LogCheckpoint>,
    @InjectRepository(Checkpoint)
    private readonly checkPointRepository: Repository<Checkpoint>,
    @InjectRepository(Timezone)
    private readonly timezoneRepository: Repository<Timezone>,
  ) {}

  /**
   * Retrieves time rotation data based on specified filters and parameters
   * @param parent_branch_id The ID of the parent branch
   * @param params Query parameters for filtering
   * @param options Additional options like timezone and user info
   * @returns Object containing time rotation data and total count
   */
  async getTimeRotation(
    parent_branch_id: string | number,
    params: TimeRotationAnalyticParams,
    options: {
      timezone?: Timezone;
      user?: User;
    },
  ) {
    // Extract and set default values for query parameters
    const {
      startDate,
      endDate,
      startTime = '00:00:00',
      endTime = '23:59:59',
      rotationInterval,
      rotationMethod,
      branchId,
      zoneId,
      zoneLabels,
      checkpointId,
      checkpointLabels,
      roleId,
      userId,
      userLabels,
      deviceId,
      deviceLabels,
      page = 1,
      limit = 10,
      orderBy,
      orderDirection = OrderDirection.DESC,
    } = params;

    const timezone = options.timezone;
    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    const timeAndRotationData: TimeAndRotation[] = [];

    // Build checkpoint query to get all checkpoints matching criteria
    const checkpointSelectQueryBuilder = this.checkPointRepository
      .createQueryBuilder('checkpoint')
      .leftJoinAndSelect('checkpoint.zone', 'zone')
      .leftJoinAndSelect('checkpoint.branch', 'branch')
      .where('checkpoint.parent_branch_id = :parentBranchId', {
        parentBranchId: parent_branch_id,
      });

    // Apply order by if provided
    if (orderBy) {
      checkpointSelectQueryBuilder.orderBy(
        `checkpoint.${orderBy}`,
        orderDirection,
      );
    }

    // Apply pagination if provided
    if (limit) {
      const start = (page - 1) * limit;
      checkpointSelectQueryBuilder.skip(start).take(limit);
    }

    // Apply branch filter if provided
    if (branchId) {
      checkpointSelectQueryBuilder.andWhere(
        'checkpoint.branch_id = :branchId',
        { branchId },
      );
    }

    // Apply zone filter if provided
    if (zoneId) {
      checkpointSelectQueryBuilder.andWhere('checkpoint.zone_id = :zoneId', {
        zoneId,
      });
    } else if (zoneLabels && zoneLabels.length > 0) {
      checkpointSelectQueryBuilder
        .leftJoin('zone.zone_labels', 'zone_labels')
        .andWhere('zone_labels.label_id IN (:...zoneLabels)', {
          zoneLabels,
        });
    }

    // Apply checkpoint filter if provided
    if (checkpointId) {
      checkpointSelectQueryBuilder.andWhere('checkpoint.id = :checkpointId', {
        checkpointId,
      });
    } else if (checkpointLabels && checkpointLabels.length > 0) {
      checkpointSelectQueryBuilder
        .leftJoin('checkpoint.checkpoint_labels', 'checkpoint_labels')
        .andWhere('checkpoint_labels.label_id IN (:...checkpointLabels)', {
          checkpointLabels,
        });
    }

    const getAllCheckpoints = await checkpointSelectQueryBuilder.getMany();

    // Parse time components
    const startTimeParts = parseTimeWithPrecision(startTime);
    const endTimeParts = parseTimeWithPrecision(endTime);

    // Generate rotation slots for each checkpoint
    for (const checkpoint of getAllCheckpoints) {
      const rotations: DateRotation[] = [];
      const diffDays = dayjs(endDate).diff(dayjs(startDate), 'day');

      // For each day in the date range
      for (let i = 0; i <= diffDays; i++) {
        const startDateTimeDaily = dayjs(startDate)
          .tz(timezone.timezone_name)
          .add(i, 'day')
          .hour(startTimeParts.hours)
          .minute(startTimeParts.minutes)
          .second(startTimeParts.seconds)
          .millisecond(startTimeParts.milliseconds);

        const endDateTimeDaily = dayjs(startDate)
          .tz(timezone.timezone_name)
          .add(i, 'day')
          .hour(endTimeParts.hours)
          .minute(endTimeParts.minutes)
          .second(endTimeParts.seconds)
          .millisecond(endTimeParts.milliseconds)
          .subtract(1, 'milliseconds');

        const intervals: IntervalRotation[] = [];

        // Create interval slots based on rotation interval
        let intervalStart = startDateTimeDaily;
        let compliance = 0;
        while (intervalStart.isBefore(endDateTimeDaily)) {
          const currentIntervalStart = intervalStart.clone();
          const intervalEnd = intervalStart.add(rotationInterval, 'minute');
          const adjustedEnd = intervalEnd.subtract(1, 'second');
          compliance += 1;
          intervals.push({
            time: currentIntervalStart.format('HH:mm'),
            dateTimeRange: {
              start: currentIntervalStart.toISOString(),
              end: adjustedEnd.toISOString(),
            },
            rotation: 0,
          });

          intervalStart = intervalEnd;
        }

        rotations.push({
          date: startDateTimeDaily.format('YYYY-MM-DD'),
          dateTimeRange: {
            start: startDateTimeDaily.toISOString(),
            end: endDateTimeDaily.toISOString(),
          },
          compliance,
          completed: 0,
          percentage: 0,
          intervals,
        });
      }

      timeAndRotationData.push({
        checkpoint_id: checkpoint.id,
        checkpoint_name: checkpoint.checkpoint_name,
        interval: rotationInterval,
        method: rotationMethod,
        rotations,
      });
    }

    // Parse date/time range with timezone adjustment
    const rangeDateAndTimeDaily = parseTimeDaily({
      startDate,
      endDate,
      startTime,
      endTime,
      offset: timezone.gmt_offset,
    });

    const checkpointIds = getAllCheckpoints.map(checkpoint => checkpoint.id);

    // Build query for log checkpoints
    const queryBuilder = this.logCheckpointRepository
      .createQueryBuilder('log_checkpoint')
      .leftJoinAndSelect('log_checkpoint.checkpoint', 'checkpoint')
      .leftJoinAndSelect('checkpoint.zone', 'zone')
      .leftJoinAndSelect('checkpoint.branch', 'branch')
      .leftJoinAndSelect('log_checkpoint.timezone', 'timezone')
      .where('log_checkpoint.parent_branch_id = :parentBranchId', {
        parentBranchId: parent_branch_id,
      })
      .andWhere('log_checkpoint.checkpoint_id IN (:...checkpointIds)', {
        checkpointIds,
      });

    // Apply date/time range filters
    if (rangeDateAndTimeDaily.length > 0) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          for (let i = 0; i < rangeDateAndTimeDaily.length; i++) {
            qb.orWhere(
              `log_checkpoint.original_submitted_time >= :startDate${i}::timestamptz AND log_checkpoint.original_submitted_time <= :endDate${i}::timestamptz`,
              {
                [`startDate${i}`]:
                  rangeDateAndTimeDaily[i].utc.startDate + '+00',
                [`endDate${i}`]: rangeDateAndTimeDaily[i].utc.endDate + '+00',
              },
            );
          }
        }),
      );
    }

    // Apply role filter
    if (roleId) {
      queryBuilder.andWhere('log_checkpoint.role_id = :roleId', {
        roleId,
      });
    }

    // Apply user filters
    if (userId) {
      queryBuilder.andWhere('log_checkpoint.user_id = :userId', {
        userId,
      });
    } else if (userLabels && userLabels.length > 0) {
      queryBuilder
        .leftJoin('log_checkpoint.user', 'user')
        .leftJoin('user.user_labels', 'user_labels')
        .andWhere('user_labels.label_id IN (:...userLabels)', {
          userLabels,
        });
    }

    // Apply device filters
    if (deviceId) {
      queryBuilder.andWhere('log_checkpoint.device_id = :deviceId', {
        deviceId,
      });
    } else if (deviceLabels && deviceLabels.length > 0) {
      queryBuilder
        .leftJoin('log_checkpoint.device', 'device')
        .leftJoin('device.device_labels', 'device_labels')
        .andWhere('device_labels.label_id IN (:...deviceLabels)', {
          deviceLabels,
        });
    }

    // Get all logs matching the criteria
    const logCheckpoints = await queryBuilder.getMany();

    // Process the logs and update the rotation data
    for (const logCheckpoint of logCheckpoints) {
      const timeAndRotation = timeAndRotationData.find(
        tr => tr.checkpoint_id === logCheckpoint.checkpoint_id,
      );
      if (!timeAndRotation) {
        continue;
      }

      // Find the correct date rotation for this log
      const dateRotation = timeAndRotation.rotations.find(r => {
        const originalSubmittedTime = dayjs(
          dayjs(logCheckpoint.original_submitted_time)
            .tz(logCheckpoint.timezone_name)
            .format('YYYY-MM-DD HH:mm'),
        );
        const dateTimeStart = dayjs(
          dayjs(r.dateTimeRange.start)
            .tz(timezone.timezone_name)
            .format('YYYY-MM-DD HH:mm'),
        );
        const dateTimeEnd = dayjs(
          dayjs(r.dateTimeRange.end)
            .tz(timezone.timezone_name)
            .format('YYYY-MM-DD HH:mm'),
        );
        // is Between or same
        return originalSubmittedTime.isBetween(
          dateTimeStart,
          dateTimeEnd,
          'day',
          '[]',
        );
      });
      if (!dateRotation) {
        continue;
      }

      // Find the correct interval for this log
      const intervalRotation = dateRotation.intervals.find(i => {
        const originalSubmittedTime = dayjs(
          dayjs(logCheckpoint.original_submitted_time)
            .tz(logCheckpoint.timezone_name)
            .format('YYYY-MM-DD HH:mm'),
        );
        const intervalStart = dayjs(
          dayjs(i.dateTimeRange.start)
            .tz(timezone.timezone_name)
            .format('YYYY-MM-DD HH:mm'),
        );
        const intervalEnd = dayjs(
          dayjs(i.dateTimeRange.end)
            .tz(timezone.timezone_name)
            .format('YYYY-MM-DD HH:mm'),
        );
        // is Between or same
        return originalSubmittedTime.isBetween(
          intervalStart,
          intervalEnd,
          'minute',
          '[]',
        );
      });
      if (!intervalRotation) {
        continue;
      }

      // Increment rotation count and update completion stats
      intervalRotation.rotation += 1;
      dateRotation.completed += 1;
      dateRotation.percentage =
        (dateRotation.completed / dateRotation.compliance) * 100;
    }

    // Return the results
    return {
      data: timeAndRotationData,
      total: timeAndRotationData.length,
    };
  }
}
