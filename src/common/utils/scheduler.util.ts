import { EFrequency } from '../modules/database/entities/frequency.entity';
import { Timezone } from '../modules/database/entities/timezone.entity';
import { Scheduler } from '../modules/database/entities/scheduler.entity';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import * as isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { DataSource, Repository } from 'typeorm';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isSameOrAfter);

/**
 * Determine next run date for a scheduler based on frequency
 */
const determineNextRunDate = (
  frequencyId: EFrequency,
  generateDate: string,
  generateTime: string,
  timezone: Timezone,
) => {
  const currentDate = dayjs().utc();

  const generateDateTime = dayjs(generateDate, 'YYYY-MM-DD')
    .tz(timezone.timezone_name)
    .set('hour', parseInt(generateTime.split(':')[0]))
    .set('minute', parseInt(generateTime.split(':')[1]))
    .set('second', 0)
    .set('millisecond', 0)
    .utc();

  const isSameOrAfter = currentDate.isSameOrAfter(
    generateDateTime,
    'milliseconds',
  );

  // console.log('currentDate: ', currentDate.format());
  // console.log('generateDateTime: ', generateDateTime.format());
  // console.log(isSameOrAfter);

  switch (frequencyId) {
    case EFrequency.DAILY:
      if (isSameOrAfter) {
        return generateDateTime.add(1, 'day').toDate();
      } else {
        return generateDateTime.toDate();
      }
    case EFrequency.WEEKLY:
      if (isSameOrAfter) {
        return generateDateTime.add(1, 'week').toDate();
      } else {
        return generateDateTime.toDate();
      }
    case EFrequency.MONTHLY:
      if (isSameOrAfter) {
        return generateDateTime.add(1, 'month').toDate();
      } else {
        return generateDateTime.toDate();
      }
    case EFrequency.YEARLY:
      if (isSameOrAfter) {
        return generateDateTime.add(1, 'year').toDate();
      } else {
        return generateDateTime.toDate();
      }
    case EFrequency.TWO_DAYS:
      if (isSameOrAfter) {
        return generateDateTime.add(2, 'days').toDate();
      } else {
        return generateDateTime.toDate();
      }
    default:
      throw new Error('Invalid frequency');
  }
};

/**
 * Determine date range based on frequency
 * @param frequency The frequency enum
 * @param currentDateTime The current date time to base calculations from
 * @returns An object containing startDate and endDate as dayjs objects
 */
const determineDateRangeByFrequency = (
  frequency: EFrequency,
  currentDateTime: dayjs.Dayjs,
) => {
  let endDate = currentDateTime.endOf('day');
  let startDate = endDate.startOf('day');

  switch (frequency) {
    case EFrequency.DAILY:
      startDate = endDate.subtract(1, 'day').startOf('day');
      break;
    case EFrequency.WEEKLY:
      startDate = endDate.subtract(1, 'week').startOf('day');
      break;
    case EFrequency.MONTHLY:
      startDate = endDate.subtract(1, 'month').startOf('day');
      break;
    case EFrequency.YEARLY:
      startDate = endDate.subtract(1, 'year').startOf('day');
      break;
    case EFrequency.TWO_DAYS:
      startDate = endDate.subtract(2, 'days').startOf('day');
      break;
    default:
      throw new Error('Invalid frequency');
  }

  return { startDate, endDate };
};

/**
 * Update next run times for schedulers in a transaction
 * @param dataSource TypeORM DataSource
 * @param schedulers Array of schedulers to update
 * @returns Promise<void>
 */
const updateSchedulersNextRunTime = async (
  dataSource: DataSource,
  schedulers: Scheduler[],
): Promise<void> => {
  if (!schedulers.length) return;

  const queryRunner = dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    for (const scheduler of schedulers) {
      const nextRunDateTime = determineNextRunDate(
        parseInt(scheduler.frequency_id as unknown as string),
        scheduler.generate_date,
        scheduler.generate_time,
        scheduler.branch.timezone,
      );
      await queryRunner.manager.update(Scheduler, scheduler.id, {
        next_run_time: nextRunDateTime,
      });
    }

    await queryRunner.commitTransaction();
  } catch (error) {
    await queryRunner.rollbackTransaction();
    throw error;
  } finally {
    await queryRunner.release();
  }
};

const updateSchedulerNextRunTime = async (
  dataSource: Repository<Scheduler>,
  scheduler: Scheduler,
) => {
  const nextRunDateTime = determineNextRunDate(
    parseInt(scheduler.frequency_id as unknown as string),
    scheduler.generate_date,
    scheduler.generate_time,
    scheduler.branch.timezone,
  );
  await dataSource.update(scheduler.id, {
    next_run_time: nextRunDateTime,
  });
};

export {
  determineNextRunDate,
  determineDateRangeByFrequency,
  updateSchedulersNextRunTime,
  updateSchedulerNextRunTime,
};
